import React from "react";
import { Gallery, Item } from "react-photoswipe-gallery";
import { IServiceRequestImage } from "types/orders";

const AuthenticatorMarkedPhotos = ({
  markerImage,
}: {
  markerImage: IServiceRequestImage[];
}) => {
  return (
    <Gallery>
      <div className="grid grid-cols-4 gap-2">
        {markerImage.map((img) => {
          const { marker_image_url, width, height } = img;
          return (
            <Item
              key={marker_image_url}
              original={marker_image_url}
              thumbnail={marker_image_url}
              width={width}
              height={height}
            >
              {({ ref, open }) => (
                <div
                  ref={ref}
                  onClick={open}
                  className="relative aspect-square cursor-pointer hover:opacity-90 transition-opacity"
                >
                  <img
                    key={marker_image_url}
                    src={marker_image_url}
                    alt="marker"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </Item>
          );
        })}
      </div>
    </Gallery>
  );
};

export default AuthenticatorMarkedPhotos;
