import clsx from "clsx";
import React from "react";
import { Gallery, Item } from "react-photoswipe-gallery";

import { IServiceRequestImage } from "types/orders";

const GalleryImage = ({
  imageList,
  className,
}: {
  imageList: IServiceRequestImage[];
  className?: string;
}) => {
  // Calculate appropriate dimensions for gallery display
  const getDisplayDimensions = (
    originalWidth: number,
    originalHeight: number
  ) => {
    // Fallback dimensions for SSR or when window is not available
    const screenWidth =
      typeof window !== "undefined" ? window.innerWidth : 1200;
    const screenHeight =
      typeof window !== "undefined" ? window.innerHeight : 800;

    const maxWidth = Math.min(screenWidth * 0.9, 1200); // 90% of screen width, max 1200px
    const maxHeight = Math.min(screenHeight * 0.9, 800); // 90% of screen height, max 800px

    const aspectRatio = originalWidth / originalHeight;

    let displayWidth = originalWidth;
    let displayHeight = originalHeight;

    // If image is too wide
    if (displayWidth > maxWidth) {
      displayWidth = maxWidth;
      displayHeight = displayWidth / aspectRatio;
    }

    // If image is too tall after width adjustment
    if (displayHeight > maxHeight) {
      displayHeight = maxHeight;
      displayWidth = displayHeight * aspectRatio;
    }

    return {
      width: Math.round(displayWidth),
      height: Math.round(displayHeight),
    };
  };

  return (
    <Gallery>
      <div className={clsx("grid grid-cols-4 gap-2", className ?? "")}>
        {imageList.map((image) => {
          const { width: displayWidth, height: displayHeight } =
            getDisplayDimensions(
              image.width || 800, // fallback width if not provided
              image.height || 600 // fallback height if not provided
            );

          return (
            <Item
              key={image.id}
              original={image.image_url}
              thumbnail={image.image_url}
              width={displayWidth}
              height={displayHeight}
            >
              {({ ref, open }) => (
                <div
                  ref={ref}
                  onClick={open}
                  className="relative aspect-square cursor-pointer hover:opacity-90 transition-opacity"
                >
                  <img
                    src={image.image_url}
                    alt={`Image ${image.id}`}
                    className="w-full h-full rounded-lg object-cover"
                  />
                  <img
                    src="/OX.svg"
                    alt="OX icon"
                    className="absolute inset-0 object-contain w-full h-full"
                  />
                </div>
              )}
            </Item>
          );
        })}
      </div>
    </Gallery>
  );
};

export default GalleryImage;
