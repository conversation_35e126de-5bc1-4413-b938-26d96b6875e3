import clsx from "clsx";
import React from "react";
import { Gallery, Item } from "react-photoswipe-gallery";
import "photoswipe/dist/photoswipe.css"; // 确保引入了 photoswipe 的 CSS

import { IServiceRequestImage } from "types/orders";

const GalleryImage = ({
  imageList,
  className,
}: {
  imageList: IServiceRequestImage[];
  className?: string;
}) => {
  // 定义传递给 PhotoSwipe 的配置项
  const options = {
    // 自定义初始缩放级别
    initialZoomLevel: (zoomLevel: any) => {
      // zoomLevel 对象包含了计算缩放级别所需的所有信息
      const image = zoomLevel.currItem.data; // 当前图片的数据
      const viewportSize = zoomLevel.viewportSize; // 视口（屏幕）的尺寸

      // 计算让图片高度撑满视口所需的缩放比例
      // 这个比例 = 视口高度 / 图片原始高度
      const zoomRatio = viewportSize.y / image.h;

      return zoomRatio;
    },
  };

  return (
    <Gallery options={options}>
      <div className={clsx("grid grid-cols-4 gap-2", className ?? "")}>
        {imageList.map((image) => (
          <Item
            key={image.id}
            original={image.image_url}
            thumbnail={image.image_url}
            width={image.width}
            height={image.height}
          >
            {({ ref, open }) => (
              <div
                ref={ref}
                onClick={open}
                className="relative aspect-square cursor-pointer hover:opacity-90 transition-opacity"
              >
                <img
                  src={image.image_url}
                  className="w-full h-full rounded-lg object-cover"
                />
                <img
                  src="/OX.svg"
                  alt="OX icon"
                  className="absolute inset-0 object-contain w-full h-full"
                />
              </div>
            )}
          </Item>
        ))}
      </div>
    </Gallery>
  );
};

export default GalleryImage;
