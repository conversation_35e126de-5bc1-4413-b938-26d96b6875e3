import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import clsx from "clsx";

import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";
import useUserHook from "hooks/useUser";
import useAppDispatch from "hooks/useAppDispatch";
import { fetchUser } from "actions/app";
import { IServiceExtraService } from "types/orders";
import { getLocalisedField } from "utils/locale";
import apiCore from "utils/apiCore";
import { parseError } from "utils/error";
import { showSuccessPopupMessage, showErrorPopupMessage } from "utils/message";
import NavigationHeader from "components/NavigationHeader";
import { homePath, ordersPath } from "components/StartAuthentication/constant";
import AppModal from "components/AppModal";

const Checkout = () => {
  const router = useRouter();
  const { locale } = router;
  const {
    getSubmitData,
    currentServiceLevel = {},
    currentAdditionalServiceList,
    product_title,
    resetForm,
  } = useNewAuthenticationForm();
  const dispatch = useAppDispatch();
  const [subtotal, setSubtotal] = useState(0);
  const { accessToken } = useUserHook();
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [referenceNumber, setReferenceNumber] = useState("");
  const [orderId, setOrderId] = useState<number | null>(null);

  console.log("currentServiceLevel", currentServiceLevel);
  useEffect(() => {
    return () => {
      resetForm();
    };
  }, []);

  useEffect(() => {
    if (!currentServiceLevel) return;

    if (!currentAdditionalServiceList?.length) {
      setSubtotal(Number(currentServiceLevel?.credit || 0));
      return;
    }

    setSubtotal(
      Number(currentServiceLevel?.credit || 0) +
        currentAdditionalServiceList?.reduce(
          (acc: number, item: IServiceExtraService) =>
            acc + Number(item.credit || 0),
          0
        ) || 0
    );
  }, [currentServiceLevel, currentAdditionalServiceList]);

  const handleSubmit = async () => {
    const params = getSubmitData();
    if (isLoading || !params.service_set_id) return;

    try {
      setIsLoading(true);
      const res = await apiCore.post(
        null,
        "v1/service_request",
        params,
        accessToken
      );
      setOrderId(res?.data?.id);
      showSuccessPopupMessage(
        "Your authentication request has been submitted."
      );
      setIsModalOpen(true);
      setReferenceNumber(res.uuid);
      dispatch(fetchUser({ accessToken }));
    } catch (error) {
      showErrorPopupMessage(parseError(error).message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="text-white">
        <NavigationHeader
          progress="100%"
          onBackClick={() => {
            resetForm();
            router.back();
          }}
        />
        <div className="max-w-4xl m-auto md:py-10 py-4 md:px-12 px-4">
          <div className="text-xl md:text-3xl font-extrabold md:mb-8 mb-4 text-center">
            Checkout
          </div>
          <div className="pb-4 md:pb-6 border-b">
            <div className="text-center text-gray-100 md:text-base text-sm">
              Authentication - {product_title?.category_title}
            </div>
            <div className="font-bold text-center flex gap-2 justify-center md:text-base text-sm">
              <div>{product_title?.brand_title}</div>
              <div>|</div>
              <div>{product_title?.model_title}</div>
            </div>
          </div>
          <div className="border-b py-4 space-y-2 md:text-base">
            <div className="flex justify-between items-center gap-4">
              <div>
                LEGIT APP Authentication Service - {currentServiceLevel?.title}
              </div>
              <div>
                {Number(currentServiceLevel?.credit || 0).toFixed(0)} $LEGIT
                Token
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-gray-100">In-app checking service</div>
              {currentAdditionalServiceList?.map(
                (service: IServiceExtraService) => (
                  <div key={service.id} className="flex justify-between">
                    <div>{getLocalisedField(service, "title", locale)}</div>
                    <div>
                      {service.is_insurance !== 1
                        ? Number(service.credit).toFixed(0) + " $LEGIT Token"
                        : "Free"}
                    </div>
                  </div>
                )
              )}
            </div>
          </div>
          <div className="border-b py-4 space-y-2">
            <div className="flex justify-between">
              <div>Subtotal</div>
              <div>{subtotal ? subtotal.toFixed(0) : 0} $LEGIT Token</div>
            </div>
          </div>
          <div className="border-b py-4 space-y-2">
            <div className="flex justify-between">
              <div>Total</div>
              <div>{subtotal ? subtotal.toFixed(0) : 0} $LEGIT Token</div>
            </div>
          </div>
          <div
            onClick={handleSubmit}
            className={clsx(
              "mt-12 text-center md:text-xl bg-gradient-red font-bold md:py-3 py-2 rounded-lg",
              isLoading ? "cursor-not-allowed opacity-80" : "cursor-pointer"
            )}
          >
            CONFIRM TO PAY
          </div>
        </div>
      </div>
      <AppModal
        title="Successful!"
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        onCancel={() => {
          router.push(homePath);
        }}
      >
        <div className="text-white">
          <div className="text-center text-gray-100 md:text-base text-xs">
            We&apos;ll handle your case as soon as possible.
          </div>
          <div className="border-b border-dotted h-1 my-3"></div>
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="text-gray-100">Item</div>
              <div className="font-bold md:text-xl">
                {product_title?.brand_title +
                  " - " +
                  product_title?.model_title}
              </div>
              {currentAdditionalServiceList?.map(
                (service: IServiceExtraService) => (
                  <div key={service.id} className="font-bold md:text-lg">
                    {getLocalisedField(service, "title", locale)}
                  </div>
                )
              )}
            </div>
            <div className="space-y-2">
              <div className="text-gray-100">Reference Number</div>
              <div className="font-bold md:text-xl">#{referenceNumber}</div>
            </div>
            <div className="space-y-2">
              <div className="text-gray-100">Total Paid</div>
              <div className="flex justify-between">
                <div className="font-bold md:text-xl">
                  {subtotal} $LEGIT Tokens
                </div>
                <div>
                  <Image
                    src="/icon_green_tick.png"
                    alt="check"
                    width={20}
                    height={20}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 md:mt-8 space-y-2">
          <div
            onClick={() => {
              if (orderId) {
                router.push(`${ordersPath}/${orderId}`);
              } else {
                router.push(ordersPath);
              }
            }}
            className="text-white text-center md:text-xl text-sm cursor-pointer bg-gradient-red font-bold py-2 rounded-lg"
          >
            CHECK THE STATUS
          </div>
          <div
            className="text-white text-center md:text-xl text-sm cursor-pointer font-bold py-2 rounded-lg border border-white"
            onClick={() => {
              router.push(homePath);
              setIsModalOpen(false);
            }}
          >
            BACK
          </div>
        </div>
      </AppModal>
    </>
  );
};

export default Checkout;
