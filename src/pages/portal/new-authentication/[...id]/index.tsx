import React, { useEffect, useMemo, useState } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import clsx from "clsx";

import { showErrorPopupMessage } from "utils/message";
import apiCore from "utils/apiCore";
import { getLocalisedFieldByObj } from "utils/locale";
import { IServicePlaceholder, IServiceSet } from "types/orders";
import useNewAuthenticationForm from "hooks/useNewAuthenticationForm";
import useUserHook from "hooks/useUser";
import AppSpin from "components/AppSpin";
import NavigationHeader from "components/NavigationHeader";
import AuthenticationService from "components/StartAuthentication/new-authentication/AuthenticationService";
import Certificate from "components/StartAuthentication/new-authentication/Certificate";
import CertificateOwner from "components/StartAuthentication/new-authentication/CertificateOwner";
import PhotosList from "components/StartAuthentication/new-authentication/PhotosList";
import CaseCustomCode from "components/StartAuthentication/new-authentication/CaseCustomCode";
import NotesToOurAuthenticators from "components/StartAuthentication/new-authentication/NotesToOurAuthenticators";
import AdditionalServices from "components/StartAuthentication/new-authentication/AdditionalServices";
import BalanceInfo from "components/StartAuthentication/BalanceInfo";
import Introduction from "components/StartAuthentication/new-authentication/Introduction";
import Instructions from "components/StartAuthentication/new-authentication/Instructions";
import {
  CATEGORY,
  checkoutPath,
} from "components/StartAuthentication/constant";
import HeaderTitle from "components/StartAuthentication/new-authentication/HeaderTitle";

const NewAuthentication = () => {
  const router = useRouter();
  const { locale } = router;
  const { id } = router.query;

  const { accessToken, user } = useUserHook();

  const [categoryId, brandId, modelId] = (id as string[]) || [];

  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingImages, setIsUploadingImages] = useState(false); // is exist any image uploading

  const {
    serviceSet,
    isHasBox,
    setServiceSet,
    setCurrentServiceLevel,
    setCurrentAdditionalServiceList,
    validateRequiredPhotos,
    validateBalance,
    setCategoryBrandModel,
    setUploadPhotoList,
    setIsHasBox,
  } = useNewAuthenticationForm();

  useEffect(() => {
    const fetchServiceSet = async () => {
      setIsLoading(true);
      try {
        const res: IServiceSet = await apiCore.get(
          null,
          `v3/service_set`,
          {
            category_id: categoryId,
            brand_id: brandId,
            model_id: modelId,
          },
          accessToken
        );
        setServiceSet(res);
        const serviceItem = res?.service_extra_service?.find(
          (service) => service.is_insurance
        );
        if (serviceItem) {
          setCurrentAdditionalServiceList([serviceItem]);
        }
        setCurrentServiceLevel(res?.service_level?.[0]);
      } finally {
        setIsLoading(false);
      }
    };

    if (categoryId && brandId && modelId) {
      setCategoryBrandModel({
        category_id: categoryId,
        brand_id: brandId,
        model_id: modelId,
      });
      if (accessToken) {
        fetchServiceSet();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryId, brandId, modelId, accessToken]);

  const handleUploadingStateChange = (isUploading: boolean) => {
    setIsUploadingImages(isUploading);
  };

  const handleSubmit = async () => {
    if (!accessToken) {
      showErrorPopupMessage("Please login to continue");
      return;
    }

    if (isUploadingImages) {
      showErrorPopupMessage("Please wait for images to finish uploading");
      return;
    }

    const balanceValidation = validateBalance(user);
    if (!balanceValidation.isValid) {
      showErrorPopupMessage(balanceValidation.message);
      return;
    }

    const validation = validateRequiredPhotos();
    // if (!validation.isValid) {
    //   const missingCount = validation.missingPhotos.length;
    //   showErrorPopupMessage(
    //     `Please upload all required photos. ${missingCount} photo${
    //       missingCount > 1 ? "s" : ""
    //     } still missing.`
    //   );
    //   return;
    // }

    router.push(`${checkoutPath}`);
  };

  const optionalPhotosList = useMemo(
    () =>
      serviceSet?.service_placeholder?.filter(
        (item: IServicePlaceholder) => item.required === 0
      ),
    [serviceSet?.service_placeholder]
  );

  const requiredPhotosList = useMemo(() => {
    if (!serviceSet?.service_placeholder) return [];

    let requiredList = serviceSet.service_placeholder.filter(
      (item: IServicePlaceholder) => item.required === 1
    );

    // For sneakers category (categoryId === "1"), filter out box_related photos if user doesn't have box
    if (Number(categoryId) === CATEGORY.SNEAKER_CATEGORY_ID && !isHasBox) {
      requiredList = requiredList.filter(
        (item: IServicePlaceholder) => !item.box_related
      );
    }

    return requiredList;
  }, [serviceSet?.service_placeholder, categoryId, isHasBox]);

  return (
    <div className="text-white">
      <NavigationHeader progress="80%" />

      <div className="max-w-screen-lg m-auto md:py-10 py-4 md:px-12 px-4">
        <div className="md:text-3xl text-xl font-bold md:mb-10 mb-4">
          New Authentication
        </div>

        {/* loading */}
        {isLoading && <AppSpin />}

        {!isLoading && (
          <div className="flex flex-col md:gap-8 gap-4">
            {/* introduction */}
            <Introduction
              locale={locale || ""}
              categoryId={categoryId}
              brandId={brandId}
              modelId={modelId}
            />
            {/* balance */}
            <BalanceInfo isShowBuyBtn={true} user={user} />
            {/* authentication service */}
            <AuthenticationService
              locale={locale || ""}
              service_level={serviceSet?.service_level}
            />
            {serviceSet?.category_id !== CATEGORY.CODE_CHECKING && (
              <>
                {/* LEGIT APP Certificate */}
                <Certificate />
                {/* LEGIT APP Certificate Owner */}
                <CertificateOwner
                  ownerName={user?.last_certificate_owner_name || user?.name}
                />
              </>
            )}

            {/* box - only show for sneakers category */}
            {Number(categoryId ?? 0) === CATEGORY.SNEAKER_CATEGORY_ID && (
              <div>
                <HeaderTitle title={"Do you have the box?"} />
                <div className="flex gap-2 border border-gray-200 rounded-sm">
                  <div
                    className={clsx(
                      "flex-1 text-center cursor-pointer p-2",
                      isHasBox && "bg-red-600"
                    )}
                    onClick={() => setIsHasBox(true)}
                  >
                    Yes
                  </div>
                  <div
                    className={clsx(
                      "flex-1 text-center cursor-pointer p-2",
                      !isHasBox && "bg-red-600"
                    )}
                    onClick={() => setIsHasBox(false)}
                  >
                    No
                  </div>
                </div>
              </div>
            )}

            <Instructions
              guidelineList={serviceSet?.service_guideline || []}
              locale={locale || ""}
            />

            {/* submission_description */}
            {serviceSet?.submission_description?.en && (
              <div className="bg-dark-100 rounded-lg sm:p-4 p-2 sm:text-sm text-xs text-gray-100">
                {getLocalisedFieldByObj(
                  serviceSet,
                  "submission_description",
                  locale
                )}
              </div>
            )}

            {/* Required Photos */}
            {!!requiredPhotosList?.length && (
              <PhotosList
                list={requiredPhotosList}
                title={"Required Photos"}
                locale={locale || "en"}
                setUploadPhotoList={setUploadPhotoList}
                onUploadingStateChange={handleUploadingStateChange}
              />
            )}

            {/* Optional photos */}
            <PhotosList
              list={optionalPhotosList}
              title={"Optional photos"}
              locale={locale || "en"}
              isOptional={true}
              setUploadPhotoList={setUploadPhotoList}
              onUploadingStateChange={handleUploadingStateChange}
            />

            {/* Case Custom Code */}
            <CaseCustomCode />
            {/* NotesToOurAuthenticators */}
            <NotesToOurAuthenticators />

            {/* Additional Services */}
            {!!serviceSet?.service_extra_service?.length && (
              <AdditionalServices
                locale={locale || ""}
                extraServiceList={serviceSet?.service_extra_service}
              />
            )}

            {/* continue */}
            <div>
              <div
                onClick={isUploadingImages ? undefined : handleSubmit}
                className={`text-center md:text-xl font-bold md:py-4 py-2 rounded-lg ${
                  isUploadingImages
                    ? "cursor-not-allowed bg-gray-500"
                    : "cursor-pointer bg-gradient-red"
                }`}
              >
                {isUploadingImages ? "Images Uploading..." : "Continue"}
              </div>
            </div>
            <div className="text-gray-500 text-center md:text-base text-xs">
              By tapping “Continue”, you agree to our{" "}
              <Link href="/terms" className="underline" target="_blank">
                Terms of Service
              </Link>
              ，{" "}
              <Link href="/privacy" className="underline" target="_blank">
                Privacy Policy
              </Link>
              ，and{" "}
              <Link
                href="/financial-guarantee"
                className="underline"
                target="_blank"
              >
                Financial Guarantee Policy
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewAuthentication;
